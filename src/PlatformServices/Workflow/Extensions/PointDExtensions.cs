using Domain.Enums;
using Geometry.Core;

namespace Workflow.Extensions;

public static class PointDExtensions
{
    private const double EdgeProximityTolerance = 0.5;

    public static double GetEdgeOnXAxis(
        this List<PointD> externalPoints,
        XAxisEdge edge)
    {
        return edge switch
        {
            XAxisEdge.Left => externalPoints.Min(point => point.X),
            XAxisEdge.Right => externalPoints.Max(point => point.X),
            _ => throw new ArgumentOutOfRangeException(nameof(edge), edge, null)
        };
    }
    
    public static bool IsCloseToEdge(
        this List<PointD> externalPoints,
        PointD point,
        XAxisEdge edge)
    {
        var xCoordinateOfEdge = externalPoints.GetEdgeOnXAxis(edge);
        
        return Math.Abs(point.X - xCoordinateOfEdge) < EdgeProximityTolerance;
    }

    public static bool SegmentIsNearlyVertical(
        this PointD point1,
        PointD point2)
    {
        var deltaX = Math.Abs(point2.X - point1.X);
        var deltaY = Math.Abs(point2.Y - point1.Y);

        // Avoid division by zero
        if (deltaX < 1e-10)
        {
            return true; // Perfectly vertical
        }

        // Calculate angle from horizontal in degrees
        var angleRadians = Math.Atan(deltaY / deltaX);
        var angleDegrees = angleRadians * 180.0 / Math.PI;

        // Check if angle is between 88 and 92 degrees (nearly vertical)
        return angleDegrees is >= 88.0 and <= 92.0;
    }

    public static PointD GetLeftmostPointExcludingVerticalLines(
        this List<PointD> externalPoints)
    {
        var minX = externalPoints.Min(p => p.X);

        // Get vertices near the leftmost part of the polygon
        var leftmostVertices = externalPoints
            .Where(point => Math.Abs(point.X - minX) < EdgeProximityTolerance)
            .ToList();

        if (leftmostVertices.Count <= 1)
        {
            return leftmostVertices.FirstOrDefault();
        }

        var validVertices = new List<PointD>();

        for (int i = 0; i < externalPoints.Count; i++)
        {
            var currentVertex = externalPoints[i];

            // Only process vertices near the rightmost part
            if (Math.Abs(currentVertex.X - minX) >= EdgeProximityTolerance)
            {
                continue;
            }

            var prevVertex = externalPoints[(i - 1 + externalPoints.Count) %
                                            externalPoints.Count];

            // Check if either adjacent segment is nearly vertical (88-92 degrees from horizontal)
            var isPrevSegmentVertical =
                prevVertex.SegmentIsNearlyVertical(currentVertex);

            // If neither adjacent segment is nearly vertical, include this vertex
            if (!isPrevSegmentVertical)
            {
                validVertices.Add(currentVertex);
            }
        }

        // If no valid vertices found, fall back to the vertex with minimum X
        if (!validVertices.Any())
        {
            return externalPoints.OrderBy(p => p.X)
                .ThenByDescending(p => p.Y)
                .First();
        }

        // Return the vertex with maximum X from valid vertices
        return validVertices.OrderBy(p => p.X)
            .ThenByDescending(p => p.Y)
            .First();
    }

    public static PointD GetRightmostPointExcludingVerticalLines(
        this List<PointD> externalPoints)
    {
        var maxX = externalPoints.Max(p => p.X);

        // Get vertices near the rightmost part of the polygon
        var rightmostVertices = externalPoints
            .Where(p => Math.Abs(p.X - maxX) < EdgeProximityTolerance)
            .ToList();

        if (rightmostVertices.Count <= 1)
        {
            return rightmostVertices.FirstOrDefault();
        }

        var validVertices = new List<PointD>();

        for (int i = 0; i < externalPoints.Count; i++)
        {
            var currentVertex = externalPoints[i];

            // Only process vertices near the rightmost part
            if (Math.Abs(currentVertex.X - maxX) >= EdgeProximityTolerance)
            {
                continue;
            }

            var nextVertex = externalPoints[(i + 1) % externalPoints.Count];

            // Check if either adjacent segment is nearly vertical (88-92 degrees from horizontal)
            bool isNextSegmentVertical =
                currentVertex.SegmentIsNearlyVertical(nextVertex);

            // If neither adjacent segment is nearly vertical, include this vertex
            if (!isNextSegmentVertical)
            {
                validVertices.Add(currentVertex);
            }
        }

        // If no valid vertices found, fall back to the vertex with maximum X
        if (!validVertices.Any())
        {
            return externalPoints.OrderByDescending(p => p.X)
                .ThenByDescending(p => p.Y)
                .First();
        }

        // Return the vertex with maximum X from valid vertices
        return validVertices.OrderByDescending(p => p.X)
            .ThenByDescending(p => p.Y)
            .First();
    }

    public static PointD FindSlopeLimitPointOnExternal(
        this List<PointD> externalPoints,
        PointD slopeLimit)
    {
        if (externalPoints.IsCloseToEdge(slopeLimit, XAxisEdge.Left))
        {
            var validLeftmostPoint = externalPoints
                .GetLeftmostPointExcludingVerticalLines();

            return new PointD(
                validLeftmostPoint.X,
                slopeLimit.Y);
        }

        if (externalPoints.IsCloseToEdge(slopeLimit, XAxisEdge.Right))
        {
            var validRightmostPoint = externalPoints
                .GetRightmostPointExcludingVerticalLines();

            return new PointD(
                validRightmostPoint.X,
                slopeLimit.Y);
        }

        return new PointD(
            slopeLimit.X,
            Helper.FindYOnPolygonEdge(slopeLimit.X, externalPoints));
    }

    public static bool SlopeLimitsAreValid(
        this List<PointD> externalPoints,
        PointD slopeLimitStart,
        PointD slopeLimitEnd)
    {
        var minX = externalPoints.Min(point => point.X);
        var maxX = externalPoints.Max(point => point.X);

        return slopeLimitStart.X >= minX && slopeLimitStart.X <= maxX &&
               slopeLimitEnd.X >= minX && slopeLimitEnd.X <= maxX;
    }
}
